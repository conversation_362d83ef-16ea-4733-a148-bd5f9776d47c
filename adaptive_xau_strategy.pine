//@version=6
strategy('XAU Adaptive Strategy - Timeframe Adaptive', overlay = true, max_bars_back = 1000)

// ===== ADVANCED ADAPTATION SYSTEM =====
enableTimeframeAdaptation = input.bool(true, '🤖 Enable Timeframe Adaptation', group = 'Adaptation System')
enablePerformanceLearning = input.bool(true, '🧠 Enable Performance Learning', group = 'Adaptation System')
enablePatternRecognition = input.bool(true, '📊 Enable Pattern Recognition', group = 'Adaptation System')
enableMultiTimeframe = input.bool(true, '⏰ Enable Multi-Timeframe Analysis', group = 'Adaptation System')
enableAdvancedRisk = input.bool(true, '⚖️ Enable Advanced Risk Management', group = 'Adaptation System')
useManualParameters = input.bool(false, '🔧 Use Manual Parameters Override', group = 'Adaptation System')

// Performance Learning Settings
learningPeriod = input.int(20, 'Learning Period (trades)', minval = 10, maxval = 50, group = 'Performance Learning')
adaptationRate = input.float(0.1, 'Adaptation Rate', minval = 0.05, maxval = 0.3, step = 0.05, group = 'Performance Learning')
minTradesForLearning = input.int(10, 'Min Trades for Learning', minval = 5, maxval = 20, group = 'Performance Learning')

// Pattern Recognition Settings
patternMemory = input.int(50, 'Pattern Memory Size', minval = 20, maxval = 100, group = 'Pattern Recognition')
minPatternOccurrence = input.int(3, 'Min Pattern Occurrence', minval = 2, maxval = 10, group = 'Pattern Recognition')

// Multi-Timeframe Settings
htfTimeframe = input.timeframe('240', 'Higher Timeframe', group = 'Multi-Timeframe')
enableHTFFilter = input.bool(true, 'Enable HTF Trend Filter', group = 'Multi-Timeframe')

// Advanced Risk Settings
useKellyPositionSizing = input.bool(false, 'Use Kelly Position Sizing', group = 'Advanced Risk')
maxPositionSize = input.float(0.1, 'Max Position Size %', minval = 0.01, maxval = 0.2, step = 0.01, group = 'Advanced Risk')
useTrailingStop = input.bool(true, 'Use Trailing Stop', group = 'Advanced Risk')

// ===== TIMEFRAME DETECTION =====
getCurrentTimeframeMins() =>
    tf = timeframe.period
    if tf == "1"
        1
    else if tf == "3"
        3
    else if tf == "5"
        5
    else if tf == "15"
        15
    else if tf == "30"
        30
    else if tf == "60" or tf == "1H"
        60
    else if tf == "240" or tf == "4H"
        240
    else if tf == "1D"
        1440
    else
        15  // Default fallback

currentTF = getCurrentTimeframeMins()

// ===== ADAPTIVE PARAMETER CALCULATION =====
getAdaptiveParameters() =>
    if not enableTimeframeAdaptation or useManualParameters
        // Use manual parameters (will be defined below)
        [14, 25.0, 35.0, 20, 2.0, 14, 70, 30, 12, 26, 50, 12, 26, 9, 14, 2.0, 2.0, 10]
    else
        // Timeframe-specific parameters
        var int adxLen = 14
        var float adxThreshold = 25.0
        var float adxStrong = 35.0
        var int bbLen = 20
        var float bbStd = 2.0
        var int rsiLen = 14
        var int rsiOB = 70
        var int rsiOS = 30
        var int fastMA = 12
        var int slowMA = 26
        var int trendMA = 50
        var int macdF = 12
        var int macdS = 26
        var int macdSig = 9
        var int atrLen = 14
        var float atrMult = 2.0
        var float rrRatio = 2.0
        var int minGap = 10

        if currentTF <= 5  // 1M, 3M, 5M - High frequency scalping
            adxLen := 21
            adxThreshold := 30.0
            adxStrong := 40.0
            bbLen := 30
            bbStd := 2.2
            rsiLen := 21
            rsiOB := 75
            rsiOS := 25
            fastMA := 8
            slowMA := 21
            trendMA := 100
            macdF := 8
            macdS := 21
            macdSig := 7
            atrLen := 21
            atrMult := 1.5  // Tighter stops for scalping
            rrRatio := 1.5
            minGap := 3
        else if currentTF <= 15  // 15M - Short-term trading
            adxLen := 14
            adxThreshold := 25.0
            adxStrong := 35.0
            bbLen := 20
            bbStd := 2.0
            rsiLen := 14
            rsiOB := 70
            rsiOS := 30
            fastMA := 12
            slowMA := 26
            trendMA := 50
            macdF := 12
            macdS := 26
            macdSig := 9
            atrLen := 14
            atrMult := 2.0
            rrRatio := 2.0
            minGap := 5
        else if currentTF <= 60  // 30M, 1H - Intraday swing
            adxLen := 10
            adxThreshold := 22.0
            adxStrong := 32.0
            bbLen := 15
            bbStd := 1.8
            rsiLen := 10
            rsiOB := 65
            rsiOS := 35
            fastMA := 9
            slowMA := 21
            trendMA := 35
            macdF := 9
            macdS := 21
            macdSig := 7
            atrLen := 10
            atrMult := 2.2
            rrRatio := 2.5
            minGap := 8
        else if currentTF <= 240  // 4H - Swing trading
            adxLen := 8
            adxThreshold := 20.0
            adxStrong := 30.0
            bbLen := 12
            bbStd := 1.6
            rsiLen := 8
            rsiOB := 60
            rsiOS := 40
            fastMA := 6
            slowMA := 18
            trendMA := 28
            macdF := 6
            macdS := 18
            macdSig := 6
            atrLen := 8
            atrMult := 2.5
            rrRatio := 3.0
            minGap := 12
        else  // Daily+ - Position trading
            adxLen := 6
            adxThreshold := 18.0
            adxStrong := 28.0
            bbLen := 10
            bbStd := 1.4
            rsiLen := 6
            rsiOB := 55
            rsiOS := 45
            fastMA := 5
            slowMA := 15
            trendMA := 25
            macdF := 5
            macdS := 15
            macdSig := 5
            atrLen := 6
            atrMult := 3.0
            rrRatio := 4.0
            minGap := 20

        [adxLen, adxThreshold, adxStrong, bbLen, bbStd, rsiLen, rsiOB, rsiOS,
         fastMA, slowMA, trendMA, macdF, macdS, macdSig, atrLen, atrMult, rrRatio, minGap]

// Get base adaptive parameters
[baseAdxLength, baseAdxThreshold, baseAdxStrong, baseBbLength, baseBbStdDev,
 baseRsiLength, baseRsiOB, baseRsiOS, baseFastMA, baseSlowMA, baseTrendMA,
 baseMacdFast, baseMacdSlow, baseMacdSignal, baseAtrLength, baseAtrMult, baseRRRatio, baseMinGap] = getAdaptiveParameters()

// ===== PERFORMANCE LEARNING SYSTEM VARIABLES =====
var array<float> tradeReturns = array.new<float>()
var array<bool> tradeOutcomes = array.new<bool>()
var array<string> tradePatterns = array.new<string>()
var array<float> parameterEffectiveness = array.new<float>()
var int totalTrades = 0
var int winningTrades = 0
var float totalReturn = 0.0
var float maxDrawdown = 0.0
var float peakEquity = 0.0

// Performance-based parameter adjustment variables (will be calculated dynamically)

// Performance metrics calculation functions
getRecentWinRate() =>
    if not enablePerformanceLearning or array.size(tradeOutcomes) < 5
        50.0
    else
        recentTrades = math.min(learningPeriod, array.size(tradeOutcomes))
        wins = 0
        for i = 0 to recentTrades - 1
            if array.get(tradeOutcomes, array.size(tradeOutcomes) - 1 - i)
                wins := wins + 1
        wins / recentTrades * 100

getAverageReturn() =>
    if array.size(tradeReturns) < 5
        0.0
    else
        recentTrades = math.min(learningPeriod, array.size(tradeReturns))
        sum = 0.0
        for i = 0 to recentTrades - 1
            sum := sum + array.get(tradeReturns, array.size(tradeReturns) - 1 - i)
        sum / recentTrades

// Calculate performance adjustments (returns tuple)
getPerformanceAdjustments() =>
    if enablePerformanceLearning and array.size(tradeReturns) >= minTradesForLearning
        recentWinRate = getRecentWinRate()
        avgReturn = getAverageReturn()

        if recentWinRate < 40 or avgReturn < -0.5
            // Poor performance - increase sensitivity, reduce risk
            newSensitivityAdj = 1.0 + adaptationRate
            newRiskAdj = 1.0 - adaptationRate * 0.5
            newThresholdAdj = 1.0 - adaptationRate * 0.3
            newGapAdj = 1.0 + adaptationRate * 0.5
            [newSensitivityAdj, newRiskAdj, newThresholdAdj, newGapAdj]
        else if recentWinRate > 70 and avgReturn > 1.0
            // Excellent performance - reduce sensitivity, increase risk
            newSensitivityAdj = 1.0 - adaptationRate * 0.5
            newRiskAdj = 1.0 + adaptationRate * 0.3
            newThresholdAdj = 1.0 + adaptationRate * 0.2
            newGapAdj = 1.0 - adaptationRate * 0.3
            [newSensitivityAdj, newRiskAdj, newThresholdAdj, newGapAdj]
        else
            // Default values
            [1.0, 1.0, 1.0, 1.0]
    else
        // Default values when learning is disabled or insufficient data
        [1.0, 1.0, 1.0, 1.0]

// Update adjustments on each bar
[sensitivityAdj, riskAdj, thresholdAdj, gapAdj] = getPerformanceAdjustments()

// Final adaptive parameters (updated dynamically)
adxLength = int(baseAdxLength * sensitivityAdj)
adxTrendThreshold = baseAdxThreshold * thresholdAdj
adxStrongTrendThreshold = baseAdxStrong * thresholdAdj
bbLength = int(baseBbLength * sensitivityAdj)
bbStdDev = baseBbStdDev
rsiLength = int(baseRsiLength * sensitivityAdj)
rsiOverbought = int(baseRsiOB)
rsiOversold = int(baseRsiOS)
fastMaLength = int(baseFastMA * sensitivityAdj)
slowMaLength = int(baseSlowMA * sensitivityAdj)
trendMaLength = int(baseTrendMA * sensitivityAdj)
macdFast = int(baseMacdFast * sensitivityAdj)
macdSlow = int(baseMacdSlow * sensitivityAdj)
macdSignal = int(baseMacdSignal * sensitivityAdj)
atrLength = int(baseAtrLength)
atrMultiplier = baseAtrMult * riskAdj
riskRewardRatio = baseRRRatio
minBarsGap = int(baseMinGap * gapAdj)


// ===== MANUAL OVERRIDE INPUTS (only shown when manual mode enabled) =====
manualAdxLength = input.int(14, 'Manual ADX Length', minval = 1, group = 'Manual Override')
manualAdxThreshold = input.float(25.0, 'Manual ADX Threshold', minval = 15.0, maxval = 40.0, step = 0.5, group = 'Manual Override')
manualAdxStrong = input.float(35.0, 'Manual ADX Strong', minval = 25.0, maxval = 50.0, step = 0.5, group = 'Manual Override')
manualBbLength = input.int(20, 'Manual BB Length', minval = 1, group = 'Manual Override')
manualBbStdDev = input.float(2.0, 'Manual BB StdDev', minval = 0.5, maxval = 5.0, step = 0.1, group = 'Manual Override')
manualRsiLength = input.int(14, 'Manual RSI Length', minval = 1, group = 'Manual Override')
manualRsiOB = input.int(70, 'Manual RSI OB', minval = 50, maxval = 100, group = 'Manual Override')
manualRsiOS = input.int(30, 'Manual RSI OS', minval = 0, maxval = 50, group = 'Manual Override')
manualFastMA = input.int(12, 'Manual Fast MA', minval = 1, group = 'Manual Override')
manualSlowMA = input.int(26, 'Manual Slow MA', minval = 1, group = 'Manual Override')
manualTrendMA = input.int(50, 'Manual Trend MA', minval = 1, group = 'Manual Override')
manualAtrMult = input.float(2.0, 'Manual ATR Mult', minval = 0.5, maxval = 5.0, step = 0.1, group = 'Manual Override')
manualRRRatio = input.float(2.0, 'Manual RR Ratio', minval = 1.0, maxval = 4.0, step = 0.1, group = 'Manual Override')

// Override with manual parameters if enabled
finalAdxLength = useManualParameters ? manualAdxLength : adxLength
finalAdxThreshold = useManualParameters ? manualAdxThreshold : adxTrendThreshold
finalAdxStrong = useManualParameters ? manualAdxStrong : adxStrongTrendThreshold
finalBbLength = useManualParameters ? manualBbLength : bbLength
finalBbStdDev = useManualParameters ? manualBbStdDev : bbStdDev
finalRsiLength = useManualParameters ? manualRsiLength : rsiLength
finalRsiOB = useManualParameters ? manualRsiOB : rsiOverbought
finalRsiOS = useManualParameters ? manualRsiOS : rsiOversold
finalFastMA = useManualParameters ? manualFastMA : fastMaLength
finalSlowMA = useManualParameters ? manualSlowMA : slowMaLength
finalTrendMA = useManualParameters ? manualTrendMA : trendMaLength
finalAtrMult = useManualParameters ? manualAtrMult : atrMultiplier
finalRRRatio = useManualParameters ? manualRRRatio : riskRewardRatio
finalMinGap = useManualParameters ? 10 : minBarsGap

// Pattern Recognition System
var array<string> successfulPatterns = array.new<string>()
var array<string> failedPatterns = array.new<string>()
var array<float> patternScores = array.new<float>()
var array<string> uniquePatterns = array.new<string>()

// ===== ADDITIONAL SETTINGS =====
riskMode = input.string('ATR Based', 'Risk Mode', options = ['ATR Based', 'Percentage'], group = 'Risk Management')
stopLossPercent = input.float(0.75, 'Stop Loss %', minval = 0.1, maxval = 2.0, step = 0.1, group = 'Risk Management')
takeProfitPercent = input.float(1.5, 'Take Profit %', minval = 0.1, maxval = 3.0, step = 0.1, group = 'Risk Management')

// ===== ADAPTIVE VOLUME AND SESSION FILTERS =====
useVolumeFilter = input.bool(true, 'Use Volume Filter', group = 'Filters')
useSessionFilter = input.bool(false, 'Use Session Filter', group = 'Filters')
londonStart = input.int(8, 'London Start Hour', minval = 0, maxval = 23, group = 'Filters')
londonEnd = input.int(12, 'London End Hour', minval = 0, maxval = 23, group = 'Filters')
nyStart = input.int(13, 'NY Start Hour', minval = 0, maxval = 23, group = 'Filters')
nyEnd = input.int(17, 'NY End Hour', minval = 0, maxval = 23, group = 'Filters')

// Adaptive volume multiplier based on timeframe
getAdaptiveVolumeMultiplier() =>
    if currentTF <= 5
        1.5  // Higher volume requirement for scalping
    else if currentTF <= 15
        1.2  // Standard for short-term
    else if currentTF <= 60
        1.0  // Lower for intraday
    else
        0.8  // Even lower for swing/position

adaptiveVolumeMultiplier = getAdaptiveVolumeMultiplier()

// ===== CALCULATE INDICATORS WITH SIMPLE INT PARAMETERS =====
// Note: Pine Script TA functions require simple int (compile-time constants)
// We use manual parameters for calculations, then apply adaptive thresholds

// ADX for Market Regime Detection
[diPlus, diMinus, adx] = ta.dmi(manualAdxLength, manualAdxLength)

// Bollinger Bands
basis = ta.sma(close, manualBbLength)
upperBB = basis + manualBbStdDev * ta.stdev(close, manualBbLength)
lowerBB = basis - manualBbStdDev * ta.stdev(close, manualBbLength)
bbPosition = (close - lowerBB) / (upperBB - lowerBB) // 0 = lower BB, 1 = upper BB

// RSI
rsi = ta.rsi(close, manualRsiLength)

// Moving Averages
fastEMA = ta.ema(close, manualFastMA)
slowEMA = ta.ema(close, manualSlowMA)
trendEMA = ta.ema(close, manualTrendMA)

// MACD with manual parameters
[macdLine, signalLine, histLine] = ta.macd(close, manualFastMA, manualSlowMA, 9)

// ATR
atr = ta.atr(14)

// Volume with adaptive multiplier
avgVolume = ta.sma(volume, 20)
volumeFilter = not useVolumeFilter or volume > avgVolume * adaptiveVolumeMultiplier

// Session Filter
currentHour = hour(time, 'GMT')
isLondonSession = currentHour >= londonStart and currentHour <= londonEnd
isNYSession = currentHour >= nyStart and currentHour <= nyEnd
sessionFilter = not useSessionFilter or isLondonSession or isNYSession

// ===== PATTERN RECOGNITION SYSTEM =====
getCurrentMarketPattern() =>
    if not enablePatternRecognition
        "NEUTRAL"
    else
        // RSI state
        rsiState = rsi > finalRsiOB ? "OB" : rsi < finalRsiOS ? "OS" : "N"

        // ADX state
        adxState = adx > finalAdxStrong ? "ST" : adx > finalAdxThreshold ? "T" : "R"

        // Price vs MA state
        priceState = close > fastEMA and fastEMA > slowEMA ? "BU" :
                     close < fastEMA and fastEMA < slowEMA ? "BE" : "N"

        // Volume state
        volState = volume > avgVolume * adaptiveVolumeMultiplier ? "HV" : "LV"

        // BB position state
        bbState = bbPosition > 0.8 ? "UB" : bbPosition < 0.2 ? "LB" : "MB"

        rsiState + adxState + priceState + volState + bbState

getPatternScore(pattern) =>
    if not enablePatternRecognition or array.size(uniquePatterns) == 0
        0.0
    else
        patternIndex = -1
        for i = 0 to array.size(uniquePatterns) - 1
            if array.get(uniquePatterns, i) == pattern
                patternIndex := i
                break

        if patternIndex >= 0
            array.get(patternScores, patternIndex)
        else
            0.0  // Neutral for unknown patterns

updatePatternPerformance(pattern, wasSuccessful) =>
    if enablePatternRecognition
        patternIndex = -1

        // Only search if array has elements
        if array.size(uniquePatterns) > 0
            for i = 0 to array.size(uniquePatterns) - 1
                if array.get(uniquePatterns, i) == pattern
                    patternIndex := i
                    break

        if patternIndex >= 0
            // Update existing pattern score
            currentScore = array.get(patternScores, patternIndex)
            newScore = currentScore * (1 - adaptationRate) + (wasSuccessful ? 1.0 : -1.0) * adaptationRate
            array.set(patternScores, patternIndex, newScore)
            true  // Return success
        else
            // Add new pattern
            array.push(uniquePatterns, pattern)
            array.push(patternScores, wasSuccessful ? 0.5 : -0.5)

            // Limit array size
            if array.size(uniquePatterns) > patternMemory
                array.shift(uniquePatterns)
                array.shift(patternScores)
            true  // Return success
    else
        false  // Pattern recognition disabled

// ===== MULTI-TIMEFRAME ANALYSIS =====
getHTFData() =>
    if not enableMultiTimeframe
        [true, true, 0.0]  // [htfBullish, htfBearish, htfStrength]
    else
        htfClose = request.security(syminfo.tickerid, htfTimeframe, close)
        htfEMA20 = request.security(syminfo.tickerid, htfTimeframe, ta.ema(close, 20))
        htfEMA50 = request.security(syminfo.tickerid, htfTimeframe, ta.ema(close, 50))
        htfRsi = request.security(syminfo.tickerid, htfTimeframe, ta.rsi(close, 14))

        htfBullish = htfClose > htfEMA20 and htfEMA20 > htfEMA50 and htfRsi > 45
        htfBearish = htfClose < htfEMA20 and htfEMA20 < htfEMA50 and htfRsi < 55
        htfStrength = math.abs(htfRsi - 50) / 50  // 0 to 1

        [htfBullish, htfBearish, htfStrength]

[htfBullish, htfBearish, htfStrength] = getHTFData()

// ===== ADVANCED SIGNAL FILTERING =====
getSignalConfluence(isBuy) =>
    if not enablePatternRecognition
        5  // Default moderate score
    else
        score = 0

        if isBuy
            // Technical confluence for buy
            score += rsi < finalRsiOS ? 3 : rsi < 50 ? 1 : 0
            score += close <= lowerBB ? 2 : bbPosition < 0.3 ? 1 : 0
            score += macdLine > signalLine ? 1 : 0
            score += volume > avgVolume * adaptiveVolumeMultiplier ? 1 : 0
            score += adx > finalAdxThreshold ? 1 : 0
            score += fastEMA > slowEMA ? 1 : 0
            score += enableMultiTimeframe and htfBullish ? 2 : 0
        else
            // Technical confluence for sell
            score += rsi > finalRsiOB ? 3 : rsi > 50 ? 1 : 0
            score += close >= upperBB ? 2 : bbPosition > 0.7 ? 1 : 0
            score += macdLine < signalLine ? 1 : 0
            score += volume > avgVolume * adaptiveVolumeMultiplier ? 1 : 0
            score += adx > finalAdxThreshold ? 1 : 0
            score += fastEMA < slowEMA ? 1 : 0
            score += enableMultiTimeframe and htfBearish ? 2 : 0

        score


// ===== ADAPTIVE MARKET REGIME DETECTION =====
isTrendingMarket = adx > adxTrendThreshold
isStrongTrendingMarket = adx > adxStrongTrendThreshold
isRangingMarket = adx <= adxTrendThreshold

// Trend Direction
bullishTrend = fastEMA > slowEMA and close > trendEMA
bearishTrend = fastEMA < slowEMA and close < trendEMA

// ===== SCENARIO A: TREND FOLLOWING =====
// Conditions for Trend Following (when ADX > threshold)
trendFollowBuyConditions = array.new<bool>()
trendFollowSellConditions = array.new<bool>()

// Buy Conditions for Trend Following
array.push(trendFollowBuyConditions, bullishTrend) // Strong bullish trend
array.push(trendFollowBuyConditions, macdLine > signalLine and macdLine > 0) // MACD bullish and above zero
array.push(trendFollowBuyConditions, close > fastEMA) // Price above fast EMA
array.push(trendFollowBuyConditions, ta.crossover(close, fastEMA) or close > close[1] and close[1] > close[2]) // Momentum confirmation
array.push(trendFollowBuyConditions, volumeFilter and sessionFilter) // Volume and session

// Sell Conditions for Trend Following
array.push(trendFollowSellConditions, bearishTrend) // Strong bearish trend
array.push(trendFollowSellConditions, macdLine < signalLine and macdLine < 0) // MACD bearish and below zero
array.push(trendFollowSellConditions, close < fastEMA) // Price below fast EMA
array.push(trendFollowSellConditions, ta.crossunder(close, fastEMA) or close < close[1] and close[1] < close[2]) // Momentum confirmation
array.push(trendFollowSellConditions, volumeFilter and sessionFilter) // Volume and session

// ===== SCENARIO B: MEAN REVERSION =====
// Conditions for Mean Reversion (when ADX < threshold)
meanReversionBuyConditions = array.new<bool>()
meanReversionSellConditions = array.new<bool>()

// Buy Conditions for Mean Reversion (with adaptive RSI levels)
array.push(meanReversionBuyConditions, close <= lowerBB or bbPosition <= 0.1) // Near lower BB
array.push(meanReversionBuyConditions, rsi <= rsiOversold) // RSI oversold (adaptive)
array.push(meanReversionBuyConditions, close > close[1]) // Price starting to bounce
array.push(meanReversionBuyConditions, close > basis * 0.998) // Not too far from middle
array.push(meanReversionBuyConditions, volumeFilter and sessionFilter) // Volume and session

// Sell Conditions for Mean Reversion (with adaptive RSI levels)
array.push(meanReversionSellConditions, close >= upperBB or bbPosition >= 0.9) // Near upper BB
array.push(meanReversionSellConditions, rsi >= rsiOverbought) // RSI overbought (adaptive)
array.push(meanReversionSellConditions, close < close[1]) // Price starting to reject
array.push(meanReversionSellConditions, close < basis * 1.002) // Not too far from middle
array.push(meanReversionSellConditions, volumeFilter and sessionFilter) // Volume and session

// ===== SIGNAL GENERATION LOGIC =====
// Count conditions met for each scenario
countTrueBool(conditions) =>
    count = 0
    for i = 0 to array.size(conditions) - 1 by 1
        if array.get(conditions, i)
            count := count + 1
            count
    count

trendBuyScore = countTrueBool(trendFollowBuyConditions)
trendSellScore = countTrueBool(trendFollowSellConditions)
meanRevBuyScore = countTrueBool(meanReversionBuyConditions)
meanRevSellScore = countTrueBool(meanReversionSellConditions)

// Enhanced Adaptive Signal Generation with Pattern Recognition
var string activeScenario = 'None'
var bool buySignal = false
var bool sellSignal = false

// Get current pattern and its score
currentPattern = getCurrentMarketPattern()
patternScore = getPatternScore(currentPattern)
buyConfluence = getSignalConfluence(true)
sellConfluence = getSignalConfluence(false)

if isTrendingMarket
    activeScenario := 'Trend Following'
    // Dynamic requirements based on pattern recognition and confluence
    baseRequired = isStrongTrendingMarket ? 4 : 3
    patternBonus = enablePatternRecognition and patternScore > 0.3 ? -1 : 0  // Lower requirement for good patterns
    confluenceBonus = buyConfluence >= 8 ? -1 : 0  // Lower requirement for high confluence

    minRequiredBuy = math.max(2, baseRequired + patternBonus + confluenceBonus)
    minRequiredSell = math.max(2, baseRequired + patternBonus + confluenceBonus)

    buySignal := trendBuyScore >= minRequiredBuy and (not enablePatternRecognition or patternScore > -0.5)
    sellSignal := trendSellScore >= minRequiredSell and (not enablePatternRecognition or patternScore > -0.5)

    // HTF filter
    if enableHTFFilter
        buySignal := buySignal and htfBullish
        sellSignal := sellSignal and htfBearish

else if isRangingMarket
    activeScenario := 'Mean Reversion'
    // Higher requirements for mean reversion + pattern confirmation
    baseRequired = 3
    patternBonus = enablePatternRecognition and patternScore > 0.2 ? -1 : 0
    confluenceBonus = buyConfluence >= 6 ? -1 : 0

    minRequiredBuy = math.max(2, baseRequired + patternBonus + confluenceBonus)
    minRequiredSell = math.max(2, baseRequired + patternBonus + confluenceBonus)

    buySignal := meanRevBuyScore >= minRequiredBuy and (not enablePatternRecognition or patternScore > -0.3)
    sellSignal := meanRevSellScore >= minRequiredSell and (not enablePatternRecognition or patternScore > -0.3)

// ===== SIGNAL FILTERING =====
// Prevent conflicting signals and add gap protection
var float lastBuyTime = na
var float lastSellTime = na

// Adaptive time calculation based on current timeframe
timeSinceLastBuy = na(lastBuyTime) ? 999 : (time - lastBuyTime) / (1000 * 60 * currentTF) // bars
timeSinceLastSell = na(lastSellTime) ? 999 : (time - lastSellTime) / (1000 * 60 * currentTF) // bars

canBuy = buySignal and timeSinceLastBuy >= minBarsGap and not sellSignal
canSell = sellSignal and timeSinceLastSell >= minBarsGap and not buySignal

// ===== ADVANCED RISK MANAGEMENT =====
// Kelly Criterion Position Sizing
getOptimalPositionSize() =>
    if not useKellyPositionSizing or array.size(tradeReturns) < 10
        0.02  // Default 2%
    else
        winRate = getRecentWinRate() / 100
        avgWin = 0.0
        avgLoss = 0.0
        wins = 0
        losses = 0

        recentTrades = math.min(learningPeriod, array.size(tradeReturns))
        for i = 0 to recentTrades - 1
            ret = array.get(tradeReturns, array.size(tradeReturns) - 1 - i)
            if ret > 0
                avgWin := avgWin + ret
                wins := wins + 1
            else if ret < 0
                avgLoss := avgLoss + math.abs(ret)
                losses := losses + 1

        if wins > 0 and losses > 0
            avgWin := avgWin / wins
            avgLoss := avgLoss / losses
            kellyPercent = (winRate * avgWin - (1 - winRate) * avgLoss) / avgWin
            math.max(0.005, math.min(maxPositionSize, kellyPercent))
        else
            0.02

// Trailing Stop System
var float longTrailStop = na
var float shortTrailStop = na

// Calculate trailing stop (returns new trailing stop value)
calculateTrailingStop(currentTrailStop, isLong) =>
    if useTrailingStop
        currentAtr = ta.atr(14)
        trailDistance = currentAtr * finalAtrMult

        if isLong
            newStop = close - trailDistance
            na(currentTrailStop) ? newStop : math.max(currentTrailStop, newStop)
        else
            newStop = close + trailDistance
            na(currentTrailStop) ? newStop : math.min(currentTrailStop, newStop)
    else
        currentTrailStop

// Update trailing stops
longTrailStop := calculateTrailingStop(longTrailStop, true)
shortTrailStop := calculateTrailingStop(shortTrailStop, false)

// Enhanced SL/TP with pattern and confluence adjustments
getSLTP(isLong) =>
    var float sl = na
    var float tp = na

    // Base risk adjustment
    baseRisk = finalAtrMult

    // Pattern-based risk adjustment
    patternRiskAdj = 1.0
    if enablePatternRecognition
        if patternScore > 0.5
            patternRiskAdj := 0.8  // Tighter stops for high-confidence patterns
        else if patternScore < -0.3
            patternRiskAdj := 1.2  // Wider stops for uncertain patterns

    // Confluence-based risk adjustment
    confluenceScore = isLong ? buyConfluence : sellConfluence
    confluenceRiskAdj = confluenceScore >= 8 ? 0.9 : confluenceScore <= 4 ? 1.1 : 1.0

    // HTF alignment adjustment
    htfRiskAdj = 1.0
    if enableMultiTimeframe
        if (isLong and htfBullish) or (not isLong and htfBearish)
            htfRiskAdj := 0.9  // Tighter stops when aligned with HTF
        else
            htfRiskAdj := 1.2  // Wider stops when against HTF

    finalRiskMult = baseRisk * patternRiskAdj * confluenceRiskAdj * htfRiskAdj

    if riskMode == 'ATR Based'
        sl := isLong ? close - atr * finalRiskMult : close + atr * finalRiskMult
        tp := isLong ? close + atr * finalRiskMult * riskRewardRatio : close - atr * finalRiskMult * riskRewardRatio
    else // Percentage
        sl := isLong ? close * (1 - stopLossPercent / 100) : close * (1 + stopLossPercent / 100)
        tp := isLong ? close * (1 + takeProfitPercent / 100) : close * (1 - takeProfitPercent / 100)

    [sl, tp]

// ===== VISUALIZATION =====
// Plot indicators
plot(basis, color = color.blue, linewidth = 1, title = 'BB Basis')
plot(upperBB, color = color.red, linewidth = 1, title = 'Upper BB')
plot(lowerBB, color = color.green, linewidth = 1, title = 'Lower BB')
plot(fastEMA, color = color.orange, linewidth = 2, title = 'Fast EMA')
plot(slowEMA, color = color.purple, linewidth = 2, title = 'Slow EMA')
plot(trendEMA, color = color.gray, linewidth = 2, title = 'Trend EMA')

// Color background based on market regime
bgColor = isTrendingMarket ? color.new(color.blue, 95) : color.new(color.yellow, 95)
bgcolor(bgColor, title = 'Market Regime')

// Enhanced signal plots with pattern and confluence info
if canBuy
    [currentSL, currentTP] = getSLTP(true)
    currentScore = isTrendingMarket ? trendBuyScore : meanRevBuyScore
    confluenceScore = getSignalConfluence(true)
    positionSize = getOptimalPositionSize()

    labelText = '🟢 BUY (' + activeScenario + ')' + '\n' + 'Score: ' + str.tostring(currentScore) + ' | Conf: ' + str.tostring(confluenceScore) + '\n' + 'Pattern: ' + currentPattern + ' (' + str.tostring(patternScore, '#.##') + ')' + '\n' + 'ADX: ' + str.tostring(adx, '#.#') + ' | HTF: ' + (htfBullish ? '✓' : '✗') + '\n' + 'Size: ' + str.tostring(positionSize * 100, '#.#') + '% | SL: ' + str.tostring(currentSL, '#.##') + '\n' + 'TP: ' + str.tostring(currentTP, '#.##')

    label.new(bar_index, low - (high - low) * 0.2, labelText, color = color.green, textcolor = color.white, style = label.style_label_up, size = size.small)

if canSell
    [currentSL, currentTP] = getSLTP(false)
    currentScore = isTrendingMarket ? trendSellScore : meanRevSellScore
    confluenceScore = getSignalConfluence(false)
    positionSize = getOptimalPositionSize()

    labelText = '🔴 SELL (' + activeScenario + ')' + '\n' + 'Score: ' + str.tostring(currentScore) + ' | Conf: ' + str.tostring(confluenceScore) + '\n' + 'Pattern: ' + currentPattern + ' (' + str.tostring(patternScore, '#.##') + ')' + '\n' + 'ADX: ' + str.tostring(adx, '#.#') + ' | HTF: ' + (htfBearish ? '✓' : '✗') + '\n' + 'Size: ' + str.tostring(positionSize * 100, '#.#') + '% | SL: ' + str.tostring(currentSL, '#.##') + '\n' + 'TP: ' + str.tostring(currentTP, '#.##')

    label.new(bar_index, high + (high - low) * 0.2, labelText, color = color.red, textcolor = color.white, style = label.style_label_down, size = size.small)

// ===== ENHANCED STRATEGY EXECUTION =====
var string lastTradePattern = ""

if canBuy
    [currentSL, currentTP] = getSLTP(true)
    positionSize = getOptimalPositionSize()

    // Execute trade with dynamic position sizing
    if useKellyPositionSizing
        strategy.entry('Buy', strategy.long, qty = positionSize * 100)
    else
        strategy.entry('Buy', strategy.long)

    // Set exits
    if useTrailingStop
        strategy.exit('Exit Long', from_entry = 'Buy', stop = currentSL, limit = currentTP, trail_points = int(atr * finalAtrMult * 10000), trail_offset = int(atr * finalAtrMult * 0.5 * 10000))
    else
        strategy.exit('Exit Long', from_entry = 'Buy', stop = currentSL, limit = currentTP)

    lastBuyTime := time
    lastTradePattern := currentPattern
    totalTrades := totalTrades + 1

if canSell
    [currentSL, currentTP] = getSLTP(false)
    positionSize = getOptimalPositionSize()

    // Execute trade with dynamic position sizing
    if useKellyPositionSizing
        strategy.entry('Sell', strategy.short, qty = positionSize * 100)
    else
        strategy.entry('Sell', strategy.short)

    // Set exits
    if useTrailingStop
        strategy.exit('Exit Short', from_entry = 'Sell', stop = currentSL, limit = currentTP, trail_points = int(atr * finalAtrMult * 10000), trail_offset = int(atr * finalAtrMult * 0.5 * 10000))
    else
        strategy.exit('Exit Short', from_entry = 'Sell', stop = currentSL, limit = currentTP)

    lastSellTime := time
    lastTradePattern := currentPattern
    totalTrades := totalTrades + 1

// ===== LEARNING SYSTEM UPDATES =====
if strategy.closedtrades > strategy.closedtrades[1]
    // Get last trade info
    lastTradeProfit = strategy.closedtrades.profit(strategy.closedtrades - 1)
    lastTradeProfitPct = lastTradeProfit / strategy.initial_capital * 100
    wasWinningTrade = lastTradeProfit > 0

    // Update performance tracking
    if enablePerformanceLearning
        array.push(tradeReturns, lastTradeProfitPct)
        array.push(tradeOutcomes, wasWinningTrade)
        array.push(tradePatterns, lastTradePattern)

        if wasWinningTrade
            winningTrades := winningTrades + 1

        totalReturn := totalReturn + lastTradeProfitPct

        // Update drawdown
        if totalReturn > peakEquity
            peakEquity := totalReturn
        currentDrawdown = (peakEquity - totalReturn) / peakEquity * 100
        if currentDrawdown > maxDrawdown
            maxDrawdown := currentDrawdown

        // Limit array sizes
        if array.size(tradeReturns) > learningPeriod * 2
            array.shift(tradeReturns)
            array.shift(tradeOutcomes)
            array.shift(tradePatterns)

    // Update pattern performance
    if enablePatternRecognition and lastTradePattern != ""
        updatePatternPerformance(lastTradePattern, wasWinningTrade)

// ===== ENHANCED ALERTS =====
if canBuy
    [currentSL, currentTP] = getSLTP(true)
    confluenceScore = getSignalConfluence(true)
    positionSize = getOptimalPositionSize()

    alertMsg = '🟢 XAU BUY SIGNAL (' + activeScenario + ')' + '\n' + '📊 Quality Score: ' + str.tostring(confluenceScore) + '/10' + '\n' + '🎯 Pattern: ' + currentPattern + ' (Score: ' + str.tostring(patternScore, '#.##') + ')' + '\n' + '📈 ADX: ' + str.tostring(adx, '#.#') + ' | HTF Aligned: ' + (htfBullish ? 'YES' : 'NO') + '\n' + '💰 Entry: ' + str.tostring(close, '#.##') + ' | Size: ' + str.tostring(positionSize * 100, '#.#') + '%' + '\n' + '🛡️ SL: ' + str.tostring(currentSL, '#.##') + ' | 🎯 TP: ' + str.tostring(currentTP, '#.##') + '\n' + '📊 Win Rate: ' + str.tostring(getRecentWinRate(), '#.#') + '%'

    alert(alertMsg, alert.freq_once_per_bar)

if canSell
    [currentSL, currentTP] = getSLTP(false)
    confluenceScore = getSignalConfluence(false)
    positionSize = getOptimalPositionSize()

    alertMsg = '🔴 XAU SELL SIGNAL (' + activeScenario + ')' + '\n' + '📊 Quality Score: ' + str.tostring(confluenceScore) + '/10' + '\n' + '🎯 Pattern: ' + currentPattern + ' (Score: ' + str.tostring(patternScore, '#.##') + ')' + '\n' + '📈 ADX: ' + str.tostring(adx, '#.#') + ' | HTF Aligned: ' + (htfBearish ? 'YES' : 'NO') + '\n' + '💰 Entry: ' + str.tostring(close, '#.##') + ' | Size: ' + str.tostring(positionSize * 100, '#.#') + '%' + '\n' + '🛡️ SL: ' + str.tostring(currentSL, '#.##') + ' | 🎯 TP: ' + str.tostring(currentTP, '#.##') + '\n' + '📊 Win Rate: ' + str.tostring(getRecentWinRate(), '#.#') + '%'

    alert(alertMsg, alert.freq_once_per_bar)

// ===== COMPREHENSIVE ADAPTIVE DASHBOARD =====
var table infoTable = table.new(position.top_right, 3, 25, bgcolor = color.white, border_width = 1)
if barstate.islast
    // Headers
    table.cell(infoTable, 0, 0, '🤖 AI TRADING SYSTEM', bgcolor = color.gray, text_color = color.white, text_size = size.normal)
    table.cell(infoTable, 1, 0, 'Value', bgcolor = color.gray, text_color = color.white)
    table.cell(infoTable, 2, 0, 'Status', bgcolor = color.gray, text_color = color.white)

    // AI System Status
    table.cell(infoTable, 0, 1, 'System Status', bgcolor = color.blue, text_color = color.white)
    systemStatus = enableTimeframeAdaptation and enablePerformanceLearning ? 'FULL AI' : 'PARTIAL'
    table.cell(infoTable, 1, 1, systemStatus, bgcolor = color.white)
    table.cell(infoTable, 2, 1, timeframe.period, bgcolor = color.white)

    // Performance Learning
    table.cell(infoTable, 0, 2, 'Learning', bgcolor = color.purple, text_color = color.white)
    table.cell(infoTable, 1, 2, enablePerformanceLearning ? 'ON' : 'OFF', bgcolor = color.white)
    table.cell(infoTable, 2, 2, str.tostring(totalTrades) + ' trades', bgcolor = enablePerformanceLearning ? color.green : color.red, text_color = color.white)

    // Pattern Recognition
    table.cell(infoTable, 0, 3, 'Patterns', bgcolor = color.orange, text_color = color.white)
    table.cell(infoTable, 1, 3, enablePatternRecognition ? 'ON' : 'OFF', bgcolor = color.white)
    table.cell(infoTable, 2, 3, str.tostring(array.size(uniquePatterns)) + ' learned', bgcolor = enablePatternRecognition ? color.green : color.red, text_color = color.white)

    // Multi-Timeframe
    table.cell(infoTable, 0, 4, 'Multi-TF', bgcolor = color.teal, text_color = color.white)
    table.cell(infoTable, 1, 4, enableMultiTimeframe ? 'ON' : 'OFF', bgcolor = color.white)
    htfStatus = enableMultiTimeframe ? (htfBullish ? 'BULL' : htfBearish ? 'BEAR' : 'NEUT') : 'OFF'
    table.cell(infoTable, 2, 4, htfStatus, bgcolor = enableMultiTimeframe ? color.green : color.red, text_color = color.white)

    // Advanced Risk
    table.cell(infoTable, 0, 5, 'Adv Risk', bgcolor = color.maroon, text_color = color.white)
    table.cell(infoTable, 1, 5, enableAdvancedRisk ? 'ON' : 'OFF', bgcolor = color.white)
    table.cell(infoTable, 2, 5, useKellyPositionSizing ? 'KELLY' : 'FIXED', bgcolor = enableAdvancedRisk ? color.green : color.red, text_color = color.white)

    // Market Regime with adaptive thresholds
    table.cell(infoTable, 0, 6, 'Market Regime', bgcolor = color.gray, text_color = color.white)
    table.cell(infoTable, 1, 6, str.tostring(adx, '#.#') + ' / ' + str.tostring(finalAdxThreshold, '#.#'), bgcolor = color.white)
    table.cell(infoTable, 2, 6, isTrendingMarket ? 'Trending' : 'Ranging', bgcolor = isTrendingMarket ? color.new(color.blue, 70) : color.new(color.yellow, 70))

    // Current Pattern
    table.cell(infoTable, 0, 7, 'Current Pattern', bgcolor = color.white)
    table.cell(infoTable, 1, 7, currentPattern, bgcolor = color.white)
    patternColor = patternScore > 0.3 ? color.green : patternScore < -0.3 ? color.red : color.gray
    table.cell(infoTable, 2, 7, str.tostring(patternScore, '#.##'), bgcolor = patternColor, text_color = color.white)

    // Performance Metrics
    table.cell(infoTable, 0, 8, 'Performance', bgcolor = color.navy, text_color = color.white)
    table.cell(infoTable, 1, 8, str.tostring(getRecentWinRate(), '#.#') + '%', bgcolor = color.white)
    winRateColor = getRecentWinRate() > 60 ? color.green : getRecentWinRate() < 40 ? color.red : color.orange
    table.cell(infoTable, 2, 8, str.tostring(getAverageReturn(), '#.##') + '%', bgcolor = winRateColor, text_color = color.white)

    // Signal Quality
    table.cell(infoTable, 0, 9, 'Signal Quality', bgcolor = color.gray, text_color = color.white)
    buyConf = getSignalConfluence(true)
    sellConf = getSignalConfluence(false)
    table.cell(infoTable, 1, 9, 'Buy: ' + str.tostring(buyConf) + '/10', bgcolor = color.white)
    table.cell(infoTable, 2, 9, 'Sell: ' + str.tostring(sellConf) + '/10', bgcolor = color.white)

    // Current Signals with Quality
    table.cell(infoTable, 0, 10, 'Signals', bgcolor = color.gray, text_color = color.white)
    buyQuality = canBuy ? (buyConf >= 8 ? 'HIGH' : buyConf >= 6 ? 'MED' : 'LOW') : 'WAIT'
    sellQuality = canSell ? (sellConf >= 8 ? 'HIGH' : sellConf >= 6 ? 'MED' : 'LOW') : 'WAIT'
    table.cell(infoTable, 1, 10, 'BUY: ' + buyQuality, bgcolor = canBuy ? color.green : color.gray, text_color = color.white)
    table.cell(infoTable, 2, 10, 'SELL: ' + sellQuality, bgcolor = canSell ? color.red : color.gray, text_color = color.white)

    // Scenario Scores
    table.cell(infoTable, 0, 11, 'Trend Following', bgcolor = color.blue, text_color = color.white)
    table.cell(infoTable, 1, 11, 'Buy: ' + str.tostring(trendBuyScore), bgcolor = color.white)
    table.cell(infoTable, 2, 11, 'Sell: ' + str.tostring(trendSellScore), bgcolor = color.white)

    table.cell(infoTable, 0, 12, 'Mean Reversion', bgcolor = color.orange, text_color = color.white)
    table.cell(infoTable, 1, 12, 'Buy: ' + str.tostring(meanRevBuyScore), bgcolor = color.white)
    table.cell(infoTable, 2, 12, 'Sell: ' + str.tostring(meanRevSellScore), bgcolor = color.white)

    // Key Indicators with adaptive levels
    table.cell(infoTable, 0, 13, 'RSI', bgcolor = color.white)
    table.cell(infoTable, 1, 13, str.tostring(rsi, '#.#') + ' (' + str.tostring(finalRsiLength) + ')', bgcolor = color.white)
    rsiStatus = rsi > finalRsiOB ? 'OB' : rsi < finalRsiOS ? 'OS' : 'N'
    table.cell(infoTable, 2, 13, rsiStatus, bgcolor = color.white)

    table.cell(infoTable, 0, 14, 'BB Position', bgcolor = color.white)
    table.cell(infoTable, 1, 14, str.tostring(bbPosition, '#.##'), bgcolor = color.white)
    bbStatus = bbPosition > 0.8 ? 'Upper' : bbPosition < 0.2 ? 'Lower' : 'Middle'
    table.cell(infoTable, 2, 14, bbStatus, bgcolor = color.white)

    table.cell(infoTable, 0, 15, 'MACD', bgcolor = color.white)
    table.cell(infoTable, 1, 15, str.tostring(macdLine, '#.##'), bgcolor = color.white)
    table.cell(infoTable, 2, 15, macdLine > signalLine ? 'Bullish' : 'Bearish', bgcolor = color.white)

    // Position Sizing
    optimalSize = getOptimalPositionSize()
    table.cell(infoTable, 0, 16, 'Position Size', bgcolor = color.white)
    table.cell(infoTable, 1, 16, str.tostring(optimalSize * 100, '#.#') + '%', bgcolor = color.white)
    table.cell(infoTable, 2, 16, useKellyPositionSizing ? 'KELLY' : 'FIXED', bgcolor = color.white)

    // Adaptive Risk Management
    [nextBuySL, nextBuyTP] = getSLTP(true)
    [nextSellSL, nextSellTP] = getSLTP(false)

    table.cell(infoTable, 0, 17, 'Risk Management', bgcolor = color.white)
    table.cell(infoTable, 1, 17, str.tostring(finalAtrMult, '#.#') + 'x ATR', bgcolor = color.white)
    table.cell(infoTable, 2, 17, 'RR: ' + str.tostring(finalRRRatio, '#.#'), bgcolor = color.white)

    table.cell(infoTable, 0, 18, 'Next Buy Levels', bgcolor = color.white)
    table.cell(infoTable, 1, 18, 'SL: ' + str.tostring(nextBuySL, '#.##'), bgcolor = color.white)
    table.cell(infoTable, 2, 18, 'TP: ' + str.tostring(nextBuyTP, '#.##'), bgcolor = color.white)

    table.cell(infoTable, 0, 19, 'Next Sell Levels', bgcolor = color.white)
    table.cell(infoTable, 1, 19, 'SL: ' + str.tostring(nextSellSL, '#.##'), bgcolor = color.white)
    table.cell(infoTable, 2, 19, 'TP: ' + str.tostring(nextSellTP, '#.##'), bgcolor = color.white)

    // Adaptive Filters Status
    table.cell(infoTable, 0, 20, 'Volume Filter', bgcolor = color.white)
    table.cell(infoTable, 1, 20, volumeFilter ? 'PASS' : 'FAIL', bgcolor = volumeFilter ? color.green : color.red, text_color = color.white)
    table.cell(infoTable, 2, 20, str.tostring(adaptiveVolumeMultiplier, '#.#') + 'x req', bgcolor = color.white)

    table.cell(infoTable, 0, 21, 'Session Filter', bgcolor = color.white)
    table.cell(infoTable, 1, 21, sessionFilter ? 'ACTIVE' : 'INACTIVE', bgcolor = sessionFilter ? color.green : color.red, text_color = color.white)
    table.cell(infoTable, 2, 21, str.tostring(currentHour) + ':00 GMT', bgcolor = color.white)

    // Learning Progress
    table.cell(infoTable, 0, 22, 'Learning Data', bgcolor = color.white)
    table.cell(infoTable, 1, 22, str.tostring(array.size(tradeReturns)) + ' / ' + str.tostring(learningPeriod), bgcolor = color.white)
    learningColor = array.size(tradeReturns) >= minTradesForLearning ? color.green : color.orange
    table.cell(infoTable, 2, 22, 'READY', bgcolor = learningColor, text_color = color.white)

    // Adaptation Status
    table.cell(infoTable, 0, 23, 'Adaptations', bgcolor = color.white)
    table.cell(infoTable, 1, 23, 'Sens: ' + str.tostring(sensitivityAdj, '#.##'), bgcolor = color.white)
    table.cell(infoTable, 2, 23, 'Risk: ' + str.tostring(riskAdj, '#.##'), bgcolor = color.white)

    // System Health
    table.cell(infoTable, 0, 24, 'System Health', bgcolor = color.white)
    systemHealth = enableTimeframeAdaptation and enablePerformanceLearning and enablePatternRecognition ? 'OPTIMAL' : 'PARTIAL'
    healthColor = systemHealth == 'OPTIMAL' ? color.green : color.orange
    table.cell(infoTable, 1, 24, systemHealth, bgcolor = healthColor, text_color = color.white)
    table.cell(infoTable, 2, 24, str.tostring(currentTF) + 'm TF', bgcolor = color.white)
