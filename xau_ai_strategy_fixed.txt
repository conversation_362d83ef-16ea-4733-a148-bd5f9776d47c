//@version=6
strategy('XAU Ultimate AI Strategy - Full Control', overlay = true, max_bars_back = 1000)

// ===== SYSTEM CONTROL TOGGLES =====
// Main AI System Controls
enableTimeframeAdaptation = input.bool(true, '🤖 Enable Timeframe Adaptation', group = 'AI System Controls')
enableSelfLearning = input.bool(true, '🧠 Enable Self-Learning System', group = 'AI System Controls')
enableMarketIntelligence = input.bool(true, '🎯 Enable Market Intelligence', group = 'AI System Controls')
enablePatternRecognition = input.bool(true, '📊 Enable Pattern Recognition', group = 'AI System Controls')
enableReinforcementLearning = input.bool(true, '🎮 Enable Reinforcement Learning', group = 'AI System Controls')
enableMultiAssetLearning = input.bool(false, '🌐 Enable Multi-Asset Learning', group = 'AI System Controls')

// Performance Controls
enablePerformanceTracking = input.bool(true, '📈 Enable Performance Tracking', group = 'Performance Controls')
enableAdaptiveRisk = input.bool(true, '⚖️ Enable Adaptive Risk Management', group = 'Performance Controls')
enableDynamicThresholds = input.bool(true, '🎛️ Enable Dynamic Thresholds', group = 'Performance Controls')
resetLearningData = input.bool(false, '🔄 Reset Learning Data', group = 'Performance Controls')

// Learning System Settings
learningRate = input.float(0.05, 'Learning Rate', minval = 0.01, maxval = 0.2, step = 0.01, group = 'Learning Settings')
maxLearningTrades = input.int(100, 'Max Learning Trades', minval = 50, maxval = 500, group = 'Learning Settings')
recentTradesMemory = input.int(20, 'Recent Trades Memory', minval = 10, maxval = 50, group = 'Learning Settings')
adaptationSensitivity = input.float(1.0, 'Adaptation Sensitivity', minval = 0.5, maxval = 2.0, step = 0.1, group = 'Learning Settings')

// Manual Override Options
useManualParameters = input.bool(false, '🔧 Use Manual Parameters', group = 'Manual Override')
manualAdxLength = input.int(14, 'Manual ADX Length', minval = 5, maxval = 50, group = 'Manual Override')
manualAdxThreshold = input.float(25.0, 'Manual ADX Threshold', minval = 15.0, maxval = 40.0, step = 0.5, group = 'Manual Override')
manualBBLength = input.int(20, 'Manual BB Length', minval = 5, maxval = 50, group = 'Manual Override')
manualRSILength = input.int(14, 'Manual RSI Length', minval = 5, maxval = 50, group = 'Manual Override')

// ===== TIMEFRAME DETECTION & ADAPTIVE PARAMETERS =====
getCurrentTimeframeMins() =>
    tf = timeframe.period
    if tf == "1"
        1
    else if tf == "3"
        3
    else if tf == "5"
        5
    else if tf == "15"
        15
    else if tf == "30"
        30
    else if tf == "60" or tf == "1H"
        60
    else if tf == "240" or tf == "4H"
        240
    else if tf == "1D"
        1440
    else
        15

currentTF = getCurrentTimeframeMins()

// ===== TIMEFRAME ADAPTIVE PARAMETERS =====
getTimeframeAdaptiveParams() =>
    if not enableTimeframeAdaptation or useManualParameters
        [manualAdxLength, manualAdxThreshold, 35.0, manualBBLength, 2.0, manualRSILength, 70, 30, 12, 26, 50, 14, 2.0, 10, 1.2]
    else
        var int adxLen = 14
        var float adxThreshold = 25.0
        var float adxStrong = 35.0
        var int bbLen = 20
        var float bbStd = 2.0
        var int rsiLen = 14
        var int rsiOB = 70
        var int rsiOS = 30
        var int fastMA = 12
        var int slowMA = 26
        var int trendMA = 50
        var int atrLen = 14
        var float atrMult = 2.0
        var int minGap = 10
        var float volMult = 1.2
        
        if currentTF <= 5  // 1M, 3M, 5M - High frequency
            adxLen := 21
            adxThreshold := 30.0
            adxStrong := 40.0
            bbLen := 30
            bbStd := 2.2
            rsiLen := 21
            rsiOB := 75
            rsiOS := 25
            fastMA := 8
            slowMA := 21
            trendMA := 100
            atrLen := 21
            atrMult := 1.8
            minGap := 5
            volMult := 1.5
        else if currentTF <= 15  // 15M
            adxLen := 14
            adxThreshold := 25.0
            adxStrong := 35.0
            bbLen := 20
            bbStd := 2.0
            rsiLen := 14
            rsiOB := 70
            rsiOS := 30
            fastMA := 12
            slowMA := 26
            trendMA := 50
            atrLen := 14
            atrMult := 2.0
            minGap := 8
            volMult := 1.2
        else if currentTF <= 60  // 30M, 1H
            adxLen := 10
            adxThreshold := 22.0
            adxStrong := 32.0
            bbLen := 15
            bbStd := 1.8
            rsiLen := 10
            rsiOB := 65
            rsiOS := 35
            fastMA := 9
            slowMA := 21
            trendMA := 35
            atrLen := 10
            atrMult := 2.2
            minGap := 12
            volMult := 1.0
        else if currentTF <= 240  // 4H
            adxLen := 8
            adxThreshold := 20.0
            adxStrong := 30.0
            bbLen := 12
            bbStd := 1.6
            rsiLen := 8
            rsiOB := 60
            rsiOS := 40
            fastMA := 6
            slowMA := 18
            trendMA := 28
            atrLen := 8
            atrMult := 2.5
            minGap := 15
            volMult := 0.8
        else  // Daily+
            adxLen := 6
            adxThreshold := 18.0
            adxStrong := 28.0
            bbLen := 10
            bbStd := 1.4
            rsiLen := 6
            rsiOB := 55
            rsiOS := 45
            fastMA := 5
            slowMA := 15
            trendMA := 25
            atrLen := 6
            atrMult := 3.0
            minGap := 20
            volMult := 0.6
        
        [adxLen, adxThreshold, adxStrong, bbLen, bbStd, rsiLen, rsiOB, rsiOS, fastMA, slowMA, trendMA, atrLen, atrMult, minGap, volMult]

// Get adaptive parameters
[adxLength, adxTrendThreshold, adxStrongTrendThreshold, bbLength, bbStdDev, 
 rsiLength, rsiOverbought, rsiOversold, fastMaLength, slowMaLength, trendMaLength, 
 atrLength, atrMultiplier, minBarsGap, volumeMultiplier] = getTimeframeAdaptiveParams()

// ===== SELF-LEARNING SYSTEM =====
// Performance tracking variables
var array<float> tradeReturns = array.new<float>()
var array<float> recentPerformance = array.new<float>()
var int totalTrades = 0
var int winningTrades = 0
var float totalProfit = 0.0
var float totalLoss = 0.0
var float rewardScore = 0.0

// Pattern recognition variables
var array<string> successPatterns = array.new<string>()
var array<string> failurePatterns = array.new<string>()

// Multi-asset learning variables
var float dxyCorrelation = 0.0
var float spyCorrelation = 0.0

// Reset learning data if requested
if resetLearningData
    array.clear(tradeReturns)
    array.clear(recentPerformance)
    array.clear(successPatterns)
    array.clear(failurePatterns)
    totalTrades := 0
    winningTrades := 0
    totalProfit := 0.0
    totalLoss := 0.0
    rewardScore := 0.0

// Performance metrics
getCurrentWinRate() =>
    if not enablePerformanceTracking or totalTrades <= 0
        50.0
    else
        winningTrades / totalTrades * 100

getCurrentProfitFactor() =>
    if not enablePerformanceTracking or totalLoss >= 0
        1.0
    else
        totalProfit / math.abs(totalLoss)

// Self-learning adjustments
getSelfLearningAdjustment() =>
    if not enableSelfLearning or array.size(recentPerformance) < 10
        1.0
    else
        recentAvg = array.avg(recentPerformance)
        
        var float perfAdjust = 1.0
        
        if recentAvg > 0.02  // Performing well
            perfAdjust := 1.0 - (learningRate * adaptationSensitivity)
        else if recentAvg < -0.01  // Performing poorly
            perfAdjust := 1.0 + (learningRate * adaptationSensitivity)
        
        perfAdjust

// ===== PATTERN RECOGNITION SYSTEM =====
// Declare variables outside functions to maintain state
var rsiState = ""
var bbState = ""
var trendState = ""
var volState = ""

// Pattern analysis
getCurrentPattern() =>
    if not enablePatternRecognition
        "NEUTRAL"
    else
        rsiState := rsi > rsiOverbought ? "OB" : rsi < rsiOversold ? "OS" : "N"
        bbState := bbPosition > 0.8 ? "U" : bbPosition < 0.2 ? "L" : "M"
        trendState := bullishTrend ? "BT" : bearishTrend ? "RT" : "N"
        volState := volRatio > 1.2 ? "HV" : volRatio < 0.8 ? "LV" : "NV"
        rsiState + bbState + trendState + volState

// Pattern score calculation
getPatternScore(pattern) =>
    if not enablePatternRecognition
        0.0
    else
        successCount = 0
        failureCount = 0
        
        if array.size(successPatterns) > 0
            for i = 0 to array.size(successPatterns) - 1
                if array.get(successPatterns, i) == pattern
                    successCount := successCount + 1
        
        if array.size(failurePatterns) > 0
            for i = 0 to array.size(failurePatterns) - 1
                if array.get(failurePatterns, i) == pattern
                    failureCount := failureCount + 1
        
        totalPatternTrades = successCount + failureCount
        if totalPatternTrades > 0
            successCount / totalPatternTrades - 0.5  // -0.5 to 0.5 range
        else
            0.0

// ===== REINFORCEMENT LEARNING SYSTEM =====
// Reward system
updateReward(tradeResult) =>
    if enableReinforcementLearning
        rewardMultiplier = adaptationSensitivity
        if tradeResult > 0
            rewardScore := rewardScore + (tradeResult * 0.1 * rewardMultiplier)
        else
            rewardScore := rewardScore - (math.abs(tradeResult) * 0.05 * rewardMultiplier)
    rewardScore

// Get reinforcement adjustment
getReinforcementAdjustment() =>
    if not enableReinforcementLearning
        1.0
    else
        // Normalize reward score to adjustment factor
        normalizedReward = rewardScore / 100.0  // Normalize
        adjustment = 1.0 + (normalizedReward * learningRate)
        math.max(0.5, math.min(2.0, adjustment))  // Clamp between 0.5 and 2.0

// ===== MULTI-ASSET LEARNING =====
// Multi-asset adjustment
getMultiAssetAdjustment() =>
    if not enableMultiAssetLearning
        1.0
    else
        // Simplified correlation adjustment
        correlationBoost = 1.0 + (math.abs(dxyCorrelation) * 0.1)
        math.max(0.8, math.min(1.2, correlationBoost))

// ===== MARKET INTELLIGENCE =====
getMarketIntelligence() =>
    if not enableMarketIntelligence
        [1.0, 0.0, 0.8, 1.0]
    else
        // Volatility regime
        currentVol = ta.atr(14)
        avgVol = ta.sma(currentVol, 50)
        volRatio = currentVol / avgVol
        
        // Trend consistency
        trendConsistency = ta.correlation(close, bar_index, 20)
        
        // Market efficiency
        priceMA = ta.sma(close, 20)
        efficiency = 1 - math.abs(close - priceMA) / priceMA
        
        // Time factor
        timeOfDay = hour(time)
        timeMultiplier = 1.0
        if currentTF <= 240
            if timeOfDay >= 8 and timeOfDay <= 12
                timeMultiplier := 1.2
            else if timeOfDay >= 13 and timeOfDay <= 17
                timeMultiplier := 1.1
            else
                timeMultiplier := 0.8
        
        [volRatio, trendConsistency, efficiency, timeMultiplier]

[volRatio, trendConsistency, efficiency, timeMultiplier] = getMarketIntelligence()

// ===== CORE INDICATORS =====
[diPlus, diMinus, adx] = ta.dmi(adxLength, adxLength)

basis = ta.sma(close, bbLength)
upperBB = basis + bbStdDev * ta.stdev(close, bbLength)
lowerBB = basis - bbStdDev * ta.stdev(close, bbLength)
bbPosition = (close - lowerBB) / (upperBB - lowerBB)

rsi = ta.rsi(close, rsiLength)
fastEMA = ta.ema(close, fastMaLength)
slowEMA = ta.ema(close, slowMaLength)
trendEMA = ta.ema(close, trendMaLength)

[macdLine, signalLine, histLine] = ta.macd(close, fastMaLength, slowMaLength, 9)
atr = ta.atr(atrLength)

avgVolume = ta.sma(volume, 20)
volumeFilter = volume > avgVolume * volumeMultiplier

// ===== ENHANCED SIGNAL GENERATION =====
// Market regime detection
isTrendingMarket = adx > adxTrendThreshold
isStrongTrendingMarket = adx > adxStrongTrendThreshold
isRangingMarket = not isTrendingMarket

// Trend detection
bullishTrend = fastEMA > slowEMA and close > trendEMA and (not enableMarketIntelligence or trendConsistency > 0)
bearishTrend = fastEMA < slowEMA and close < trendEMA and (not enableMarketIntelligence or trendConsistency < 0)

// ===== COMBINED ADJUSTMENTS =====
// Combine all AI adjustments
selfLearningAdjustment = getSelfLearningAdjustment()
reinforcementAdjustment = getReinforcementAdjustment()
multiAssetAdjustment = getMultiAssetAdjustment()

// Combined adjustment factor
combinedAdjustment = selfLearningAdjustment * reinforcementAdjustment * multiAssetAdjustment
finalAdjustment = math.max(0.5, math.min(2.0, combinedAdjustment))

// Apply adjustments
adjustedAtrMultiplier = atrMultiplier * (enableAdaptiveRisk ? finalAdjustment : 1.0)
adjustedMinGap = int(minBarsGap * (enableAdaptiveRisk ? finalAdjustment : 1.0))

// Dynamic threshold adjustment
dynamicAdjustment = enableDynamicThresholds ? (volRatio * 0.3 + math.abs(trendConsistency) * 0.3 + efficiency * 0.4) * timeMultiplier : 1.0
adjustedAdxThreshold = adxTrendThreshold * (enableDynamicThresholds ? (0.8 + dynamicAdjustment * 0.4) : 1.0)

// Pattern-enhanced signals
currentPattern = getCurrentPattern()
patternScore = getPatternScore(currentPattern)
patternBoost = enablePatternRecognition ? (1.0 + patternScore * 0.5) : 1.0

// Core signal conditions
trendBuySignal = isTrendingMarket and bullishTrend and macdLine > signalLine and close > fastEMA and volumeFilter
trendSellSignal = isTrendingMarket and bearishTrend and macdLine < signalLine and close < fastEMA and volumeFilter

meanRevBuySignal = isRangingMarket and close <= lowerBB and rsi <= rsiOversold and close > close[1] and volumeFilter
meanRevSellSignal = isRangingMarket and close >= upperBB and rsi >= rsiOverbought and close < close[1] and volumeFilter

// Signal gap protection
var float lastSignalTime = 0
timeSinceLastSignal = (time - lastSignalTime) / (1000 * 60 * currentTF)
canTrade = timeSinceLastSignal >= adjustedMinGap

// Final signals with pattern boost
finalBuySignal = (trendBuySignal or meanRevBuySignal) and canTrade and (not enablePatternRecognition or patternScore > -0.3)
finalSellSignal = (trendSellSignal or meanRevSellSignal) and canTrade and (not enablePatternRecognition or patternScore > -0.3)

// ===== ADAPTIVE RISK MANAGEMENT =====
getAdaptiveRiskManagement(isLong) =>
    baseRisk = atr * adjustedAtrMultiplier
    
    // Market condition adjustments
    riskAdjustment = 1.0
    if enableMarketIntelligence
        if volRatio > 1.5
            riskAdjustment := 1.3
        else if volRatio < 0.7
            riskAdjustment := 0.8
    
    // Timeframe adjustment
    timeframeRisk = enableTimeframeAdaptation ? (currentTF <= 15 ? 0.8 : currentTF >= 240 ? 1.2 : 1.0) : 1.0
    
    // Pattern adjustment
    patternRisk = enablePatternRecognition ? (1.0 - patternScore * 0.2) : 1.0
    
    finalRisk = baseRisk * riskAdjustment * timeframeRisk * patternRisk * patternBoost
    
    sl = isLong ? close - finalRisk : close + finalRisk
    tp = isLong ? close + finalRisk * 2.0 : close - finalRisk * 2.0
    
    [sl, tp]

// ===== TRADE EXECUTION =====
if finalBuySignal
    [sl, tp] = getAdaptiveRiskManagement(true)
    strategy.entry("Buy", strategy.long)
    strategy.exit("Exit Long", from_entry="Buy", stop=sl, limit=tp)
    lastSignalTime := time
    totalTrades := totalTrades + 1

if finalSellSignal
    [sl, tp] = getAdaptiveRiskManagement(false)
    strategy.entry("Sell", strategy.short)
    strategy.exit("Exit Short", from_entry="Sell", stop=sl, limit=tp)
    lastSignalTime := time
    totalTrades := totalTrades + 1

// ===== LEARNING SYSTEM UPDATES =====
if strategy.closedtrades > strategy.closedtrades[1]
    lastTradeProfit = strategy.closedtrades.profit(strategy.closedtrades - 1)
    lastTradeProfitPct = lastTradeProfit / strategy.initial_capital * 100
    
    // Update learning systems
    if enableSelfLearning or enablePerformanceTracking
        if lastTradeProfit > 0
            winningTrades := winningTrades + 1
            totalProfit := totalProfit + lastTradeProfit
            
            // Add to success patterns
            if enablePatternRecognition
                array.push(successPatterns, currentPattern)
                if array.size(successPatterns) > maxLearningTrades
                    array.shift(successPatterns)
        else
            totalLoss := totalLoss + lastTradeProfit
            
            // Add to failure patterns
            if enablePatternRecognition
                array.push(failurePatterns, currentPattern)
                if array.size(failurePatterns) > maxLearningTrades
                    array.shift(failurePatterns)
        
        // Update recent performance
        array.push(recentPerformance, lastTradeProfitPct)
        if array.size(recentPerformance) > recentTradesMemory
            array.shift(recentPerformance)
        
        // Update trade returns
        array.push(tradeReturns, lastTradeProfitPct)
        if array.size(tradeReturns) > maxLearningTrades
            array.shift(tradeReturns)
    
    // Update reinforcement learning
    if enableReinforcementLearning
        updateReward(lastTradeProfitPct)

// ===== VISUALIZATION =====
// Plot indicators
plot(basis, color=color.blue, linewidth=1, title="BB Basis")
plot(upperBB, color=color.red, linewidth=1, title="Upper BB")
plot(lowerBB, color=color.green, linewidth=1, title="Lower BB")
plot(fastEMA, color=color.orange, linewidth=2, title="Fast EMA")
plot(slowEMA, color=color.purple, linewidth=2, title="Slow EMA")
plot(trendEMA, color=color.gray, linewidth=2, title="Trend EMA")

// Background color
bgColor = isTrendingMarket ? color.new(color.blue, 95) : color.new(color.yellow, 95)
bgcolor(bgColor)

// Signal plots
plotshape(finalBuySignal, style=shape.labelup, location=location.belowbar, color=color.green, size=size.normal)
plotshape(finalSellSignal, style=shape.labeldown, location=location.abovebar, color=color.red, size=size.normal)

// ===== COMPREHENSIVE DASHBOARD =====
var table infoTable = table.new(position.top_right, 3, 12, bgcolor=color.white, border_width=1)
if barstate.islast
    // Headers
    table.cell(infoTable, 0, 0, "🤖 AI TRADING SYSTEM", bgcolor=color.gray, text_color=color.white, text_size=size.small)
    table.cell(infoTable, 1, 0, "Value", bgcolor=color.gray, text_color=color.white, text_size=size.small)
    table.cell(infoTable, 2, 0, "Status", bgcolor=color.gray, text_color=color.white, text_size=size.small)
    
    // System Status
    table.cell(infoTable, 0, 1, "System Mode", bgcolor=color.blue, text_color=color.white, text_size=size.tiny)
    systemMode = useManualParameters ? "Manual" : "AI Auto"
    table.cell(infoTable, 1, 1, systemMode, bgcolor=color.white, text_size=size.tiny)
    modeColor = useManualParameters ? color.orange : color.green
    table.cell(infoTable, 2, 1, "Active", bgcolor=modeColor, text_color=color.white, text_size=size.tiny)
    
    // AI Modules Status
    table.cell(infoTable, 0, 2, "Timeframe AI", bgcolor=color.white, text_size=size.tiny)
    table.cell(infoTable, 1, 2, enableTimeframeAdaptation ? "ON" : "OFF", bgcolor=color.white, text_size=size.tiny)
    table.cell(infoTable, 2, 2, timeframe.period, bgcolor=enableTimeframeAdaptation ? color.green : color.red, text_color=color.white, text_size=size.tiny)
    
    table.cell(infoTable, 0, 3, "Self Learning", bgcolor=color.white, text_size=size.tiny)
    table.cell(infoTable, 1, 3, enableSelfLearning ? "ON" : "OFF", bgcolor=color.white, text_size=size.tiny)
    table.cell(infoTable, 2, 3, str.tostring(totalTrades), bgcolor=enableSelfLearning ? color.green : color.red, text_color=color.white, text_size=size.tiny)
    
    table.cell(infoTable, 0, 4, "Market Intel", bgcolor=color.white, text_size=size.tiny)
    table.cell(infoTable, 1, 4, enableMarketIntelligence ? "ON" : "OFF", bgcolor=color.white, text_size=size.tiny)
    table.cell(infoTable, 2, 4, str.tostring(efficiency, "#.##"), bgcolor=enableMarketIntelligence ? color.green : color.red, text_color=color.white, text_size=size.tiny)
    
    table.cell(infoTable, 0, 5, "Pattern Recog", bgcolor=color.white, text_size=size.tiny)
    table.cell(infoTable, 1, 5, enablePatternRecognition ? "ON" : "OFF", bgcolor=color.white, text_size=size.tiny)
    table.cell(infoTable, 2, 5, str.tostring(patternScore, "#.##"), bgcolor=enablePatternRecognition ? color.green : color.red, text_color=color.white, text_size=size.tiny)
    
    table.cell(infoTable, 0, 6, "Reinforcement", bgcolor=color.white, text_size=size.tiny)
    table.cell(infoTable, 1, 6, enableReinforcementLearning ? "ON" : "OFF", bgcolor=color.white, text_size=size.tiny)
    table.cell(infoTable, 2, 6, str.tostring(rewardScore, "#.#"), bgcolor=enableReinforcementLearning ? color.green : color.red, text_color=color.white, text_size=size.tiny)
    
    // Current Parameters
    table.cell(infoTable, 0, 7, "ADX", bgcolor=color.yellow, text_color=color.black, text_size=size.tiny)
    table.cell(infoTable, 1, 7, str.tostring(adxLength), bgcolor=color.white, text_size=size.tiny)
    table.cell(infoTable, 2, 7, str.tostring(adx, "#.#"), bgcolor=color.white, text_size=size.tiny)
    
    table.cell(infoTable, 0, 8, "BB", bgcolor=color.yellow, text_color=color.black, text_size=size.tiny)
    table.cell(infoTable, 1, 8, str.tostring(bbLength), bgcolor=color.white, text_size=size.tiny)
    table.cell(infoTable, 2, 8, str.tostring(bbPosition, "#.##"), bgcolor=color.white, text_size=size.tiny)
    
    table.cell(infoTable, 0, 9, "RSI", bgcolor=color.yellow, text_color=color.black, text_size=size.tiny)
    table.cell(infoTable, 1, 9, str.tostring(rsiLength), bgcolor=color.white, text_size=size.tiny)
    table.cell(infoTable, 2, 9, str.tostring(rsi, "#.#"), bgcolor=color.white, text_size=size.tiny)
    
    // Market Analysis
    table.cell(infoTable, 0, 10, "Market Regime", bgcolor=color.purple, text_color=color.white, text_size=size.tiny)
    marketRegime = isTrendingMarket ? "Trending" : "Ranging"
    table.cell(infoTable, 1, 10, marketRegime, bgcolor=color.white, text_size=size.tiny)
    regimeColor = isTrendingMarket ? color.blue : color.orange
    table.cell(infoTable, 2, 10, "Active", bgcolor=regimeColor, text_color=color.white, text_size=size.tiny)
    
    // Performance
    table.cell(infoTable, 0, 11, "Win Rate", bgcolor=color.green, text_color=color.white, text_size=size.tiny)
    table.cell(infoTable, 1, 11, str.tostring(getCurrentWinRate(), "#.#") + "%", bgcolor=color.white, text_size=size.tiny)
    winRateColor = getCurrentWinRate() > 50 ? color.green : color.red
    table.cell(infoTable, 2, 11, "OK", bgcolor=winRateColor, text_color=color.white, text_size=size.tiny)